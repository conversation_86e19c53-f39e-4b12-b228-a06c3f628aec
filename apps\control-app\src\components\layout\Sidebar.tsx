'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import {
  HomeIcon,
  BuildingOfficeIcon,
  TruckIcon,
  MapIcon,
  ClockIcon,
  UsersIcon,
  CurrencyDollarIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { TruckIcon as TruckIconSolid } from '@heroicons/react/24/solid';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { clsx } from 'clsx';

interface SidebarProps {
  open?: boolean;
  onClose?: () => void;
  mobile?: boolean;
}

/**
 * Navigation principale de l'application Control
 */
const navigation = [
  { name: 'Tableau de bord', href: '/', icon: HomeIcon },
  {
    name: 'Donn<PERSON> Ma<PERSON>',
    icon: BuildingOfficeIcon,
    children: [
      { name: 'Agences', href: '/agencies', icon: BuildingOfficeIcon },
      { name: '<PERSON><PERSON><PERSON>', href: '/fleet', icon: TruckIcon },
      { name: 'Itinéraires', href: '/routes', icon: MapIcon },
      { name: '<PERSON><PERSON><PERSON>', href: '/schedules', icon: ClockIcon },
    ],
  },
  {
    name: 'Utilisateurs',
    icon: UsersIcon,
    children: [
      { name: 'Gestion des utilisateurs', href: '/users', icon: UsersIcon },
      { name: 'Rôles et permissions', href: '/roles', icon: ShieldCheckIcon },
    ],
  },
  {
    name: 'Configuration',
    icon: CurrencyDollarIcon,
    children: [
      { name: 'Tarifs', href: '/pricing', icon: CurrencyDollarIcon },
      { name: 'Règles métier', href: '/business-rules', icon: ShieldCheckIcon },
    ],
  },
  {
    name: 'Maintenance',
    icon: WrenchScrewdriverIcon,
    children: [
      { name: 'Planification', href: '/maintenance', icon: WrenchScrewdriverIcon },
      { name: 'Ordres de réparation', href: '/repairs', icon: WrenchScrewdriverIcon },
      { name: 'Stock de pièces', href: '/parts', icon: WrenchScrewdriverIcon },
    ],
  },
  { name: 'Rapports', href: '/reports', icon: ChartBarIcon },
  { name: 'Journal d\'audit', href: '/audit', icon: ShieldCheckIcon },
];

/**
 * Composant Sidebar
 */
export function Sidebar({ open = true, onClose, mobile = false }: SidebarProps) {
  const pathname = usePathname();

  const SidebarContent = () => (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 border-r border-neutral-200">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center">
        <TruckIconSolid className="h-8 w-8 text-primary-600" />
        <span className="ml-2 text-xl font-bold text-neutral-900">
          SAMATRANSPORT
        </span>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => (
                <li key={item.name}>
                  {!item.children ? (
                    <Link
                      href={item.href!}
                      className={clsx(
                        pathname === item.href
                          ? 'bg-neutral-50 text-primary-600'
                          : 'text-neutral-700 hover:text-primary-600 hover:bg-neutral-50',
                        'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                      )}
                    >
                      <item.icon
                        className={clsx(
                          pathname === item.href
                            ? 'text-primary-600'
                            : 'text-neutral-400 group-hover:text-primary-600',
                          'h-6 w-6 shrink-0'
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  ) : (
                    <div>
                      <div className="text-xs font-semibold leading-6 text-neutral-400 uppercase tracking-wide">
                        {item.name}
                      </div>
                      <ul role="list" className="mt-2 space-y-1">
                        {item.children.map((subItem) => (
                          <li key={subItem.name}>
                            <Link
                              href={subItem.href}
                              className={clsx(
                                pathname === subItem.href
                                  ? 'bg-neutral-50 text-primary-600'
                                  : 'text-neutral-700 hover:text-primary-600 hover:bg-neutral-50',
                                'group flex gap-x-3 rounded-md p-2 pl-8 text-sm leading-6 font-semibold'
                              )}
                            >
                              <subItem.icon
                                className={clsx(
                                  pathname === subItem.href
                                    ? 'text-primary-600'
                                    : 'text-neutral-400 group-hover:text-primary-600',
                                  'h-6 w-6 shrink-0'
                                )}
                                aria-hidden="true"
                              />
                              {subItem.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </li>
        </ul>
      </nav>
    </div>
  );

  if (mobile) {
    return (
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose!}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-neutral-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={onClose}
                    >
                      <span className="sr-only">Fermer la sidebar</span>
                      <XMarkIcon
                        className="h-6 w-6 text-white"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Transition.Child>
                <SidebarContent />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>
    );
  }

  return <SidebarContent />;
}
