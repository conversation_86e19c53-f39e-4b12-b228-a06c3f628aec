/**
 * Types générés automatiquement pour la base de données Supabase
 * Ces types correspondent au schéma de la base de données SAMATRANSPORT
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      agencies: {
        Row: {
          id: string
          name: string
          code: string
          address: string
          city: string
          country: string
          phone: string
          email: string | null
          currency: 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          code: string
          address: string
          city: string
          country?: string
          phone: string
          email?: string | null
          currency?: 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          code?: string
          address?: string
          city?: string
          country?: string
          phone?: string
          email?: string | null
          currency?: 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      vehicles: {
        Row: {
          id: string
          agency_id: string
          registration_number: string
          brand: string
          model: string
          year: number
          type: 'STANDARD' | 'VIP'
          capacity: number
          status: 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE'
          last_maintenance: string | null
          next_maintenance: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          agency_id: string
          registration_number: string
          brand: string
          model: string
          year: number
          type: 'STANDARD' | 'VIP'
          capacity: number
          status?: 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE'
          last_maintenance?: string | null
          next_maintenance?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          agency_id?: string
          registration_number?: string
          brand?: string
          model?: string
          year?: number
          type?: 'STANDARD' | 'VIP'
          capacity?: number
          status?: 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE'
          last_maintenance?: string | null
          next_maintenance?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "vehicles_agency_id_fkey"
            columns: ["agency_id"]
            referencedRelation: "agencies"
            referencedColumns: ["id"]
          }
        ]
      }
      routes: {
        Row: {
          id: string
          agency_id: string
          name: string
          origin: string
          destination: string
          distance_km: number
          estimated_duration: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          agency_id: string
          name: string
          origin: string
          destination: string
          distance_km: number
          estimated_duration: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          agency_id?: string
          name?: string
          origin?: string
          destination?: string
          distance_km?: number
          estimated_duration?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "routes_agency_id_fkey"
            columns: ["agency_id"]
            referencedRelation: "agencies"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'DRIVER'
          agency_id: string | null
          is_active: boolean
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'DRIVER'
          agency_id?: string | null
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'DRIVER'
          agency_id?: string | null
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_agency_id_fkey"
            columns: ["agency_id"]
            referencedRelation: "agencies"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      vehicle_type: 'STANDARD' | 'VIP'
      vehicle_status: 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE'
      user_role: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'DRIVER'
      booking_status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED'
      payment_status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED'
      package_status: 'PENDING' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
