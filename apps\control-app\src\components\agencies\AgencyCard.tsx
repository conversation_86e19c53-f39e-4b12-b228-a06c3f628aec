'use client';

import { useState } from 'react';
import type { Database } from '@samatransport/lib-core';

type Agency = Database['public']['Tables']['agencies']['Row'];

interface AgencyCardProps {
  agency: Agency;
  onEdit: (agency: Agency) => void;
  onView: (agency: Agency) => void;
  onDelete: (agency: Agency) => void;
  onToggleStatus: (agency: Agency) => void;
}

/**
 * Carte d'affichage d'une agence
 */
export function AgencyCard({ 
  agency, 
  onEdit, 
  onView, 
  onDelete, 
  onToggleStatus 
}: AgencyCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  /**
   * Formate la date de création
   */
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  /**
   * Retourne l'icône de la devise
   */
  const getCurrencyIcon = (currency: string) => {
    const icons: Record<string, string> = {
      'XOF': 'CFA',
      'GNF': 'GNF',
      'LRD': 'L$',
      'SLL': 'Le',
      'EUR': '€',
      'USD': '$'
    };
    return icons[currency] || currency;
  };

  /**
   * Retourne l'emoji du pays
   */
  const getCountryFlag = (country: string) => {
    const flags: Record<string, string> = {
      "Côte d'Ivoire": '🇨🇮',
      'Guinée': '🇬🇳',
      'Liberia': '🇱🇷',
      'Sierra Leone': '🇸🇱',
      'Mali': '🇲🇱',
      'Burkina Faso': '🇧🇫',
      'Ghana': '🇬🇭',
      'Sénégal': '🇸🇳'
    };
    return flags[country] || '🌍';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 hover:shadow-md transition-shadow duration-200">
      {/* En-tête de la carte */}
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-neutral-900 truncate">
                {agency.name}
              </h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                agency.is_active 
                  ? 'bg-success-100 text-success-800' 
                  : 'bg-neutral-100 text-neutral-800'
              }`}>
                {agency.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <div className="mt-1 flex items-center space-x-2">
              <span className="text-sm font-medium text-primary-600">
                {agency.code}
              </span>
              <span className="text-neutral-300">•</span>
              <span className="text-sm text-neutral-500">
                {getCountryFlag(agency.country)} {agency.city}
              </span>
            </div>
          </div>

          {/* Menu d'actions */}
          <div className="relative">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-neutral-400 hover:text-neutral-600 rounded-lg hover:bg-neutral-50"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>

            {isMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onView(agency);
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                  >
                    <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Voir les détails
                  </button>
                  
                  <button
                    onClick={() => {
                      onEdit(agency);
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                  >
                    <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Modifier
                  </button>

                  <button
                    onClick={() => {
                      onToggleStatus(agency);
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                  >
                    {agency.is_active ? (
                      <>
                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Désactiver
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-4v4m0 0V9a3 3 0 013-3h4a3 3 0 013 3v1M9 21h6" />
                        </svg>
                        Activer
                      </>
                    )}
                  </button>

                  <div className="border-t border-neutral-100 my-1"></div>
                  
                  <button
                    onClick={() => {
                      onDelete(agency);
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Supprimer
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Informations de l'agence */}
        <div className="mt-4 space-y-2">
          <div className="flex items-center text-sm text-neutral-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span className="truncate">{agency.address}</span>
          </div>

          <div className="flex items-center text-sm text-neutral-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span>{agency.phone}</span>
          </div>

          {agency.email && (
            <div className="flex items-center text-sm text-neutral-600">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span className="truncate">{agency.email}</span>
            </div>
          )}
        </div>

        {/* Pied de carte */}
        <div className="mt-4 pt-4 border-t border-neutral-100 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-neutral-500">Devise:</span>
            <span className="text-xs font-semibold text-neutral-700">
              {getCurrencyIcon(agency.currency)}
            </span>
          </div>
          
          <div className="text-xs text-neutral-500">
            Créée le {formatDate(agency.created_at)}
          </div>
        </div>
      </div>

      {/* Overlay pour fermer le menu */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </div>
  );
}
