'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Button, Input, Card } from '@samatransport/ui';
import { TruckIcon } from '@heroicons/react/24/solid';
import { EyeIcon, EyeSlashIcon, EnvelopeIcon, LockClosedIcon } from '@heroicons/react/24/outline';

/**
 * Page de connexion moderne pour l'application Control SAMATRANSPORT
 */
export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  const redirectTo = searchParams.get('redirectTo') || '/';

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const { error } = await signIn(email, password);

      if (error) {
        setError(typeof error === 'string' ? error : (error as any)?.message || 'Une erreur est survenue lors de la connexion');
      } else {
        router.push(redirectTo);
      }
    } catch (err) {
      setError('Une erreur inattendue s\'est produite');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-600 via-neutral-500 to-neutral-700">
      {/* Layout principal à deux colonnes */}
      <div className="min-h-screen flex flex-col lg:flex-row">

        {/* COLONNE GAUCHE - Section d'accueil */}
        <div className="flex-1 flex items-center justify-center p-8 lg:p-12">
          <div className="max-w-lg w-full space-y-8 animate-in fade-in-0 slide-in-from-left-8 duration-700">

            {/* Logo principal avec effet de brillance */}
            <div className="text-center lg:text-left">
              <div className="flex justify-center lg:justify-start mb-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-white rounded-full blur-2xl opacity-30 animate-pulse"></div>
                  <div className="relative bg-white p-6 rounded-full shadow-strong border border-primary-100">
                    <TruckIcon className="h-16 w-16 text-primary-600" />
                  </div>
                </div>
              </div>

              {/* Titre principal */}
              <h1 className="text-4xl lg:text-5xl font-bold text-white tracking-tight drop-shadow-lg mb-4">
                SAMATRANSPORT
                <span className="block text-2xl lg:text-3xl font-semibold text-primary-200 mt-2">
                  Control
                </span>
              </h1>

              {/* Message de bienvenue */}
              <p className="text-lg text-neutral-200 font-medium drop-shadow-md mb-8">
                Plateforme de gestion administrative pour les services de transport
              </p>
            </div>

            {/* Fonctionnalités de l'application */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-white drop-shadow-md">
                Fonctionnalités principales
              </h2>

              <div className="space-y-4">
                {[
                  {
                    icon: "🚌",
                    title: "Gestion des agences",
                    description: "Administration complète des agences de transport"
                  },
                  {
                    icon: "📊",
                    title: "Tableaux de bord",
                    description: "Suivi en temps réel des performances"
                  },
                  {
                    icon: "👥",
                    title: "Gestion des utilisateurs",
                    description: "Contrôle des accès et permissions"
                  },
                  {
                    icon: "🔒",
                    title: "Sécurité avancée",
                    description: "Protection des données et authentification"
                  }
                ].map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-4 p-4 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300"
                  >
                    <span className="text-2xl">{feature.icon}</span>
                    <div>
                      <h3 className="font-semibold text-white text-sm">{feature.title}</h3>
                      <p className="text-neutral-200 text-xs mt-1">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* COLONNE DROITE - Formulaire de connexion */}
        <div className="flex-1 flex items-center justify-center p-8 lg:p-12 bg-white/5 backdrop-blur-sm">
          <div className="max-w-md w-full space-y-8 animate-in fade-in-0 slide-in-from-right-8 duration-700 delay-300">

            {/* En-tête du formulaire */}
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold text-white drop-shadow-lg">
                Connexion
              </h2>
              <p className="text-sm text-neutral-200 font-medium drop-shadow-md">
                Connectez-vous à votre compte administrateur
              </p>
            </div>

            {/* Formulaire de connexion modernisé */}
            <Card className="backdrop-blur-sm bg-white/95 border-0 shadow-strong">
              <form className="space-y-6 p-8" onSubmit={handleSignIn}>
            {/* Message d'erreur avec design amélioré */}
            {error && (
              <div className="rounded-lg bg-error-50 border border-error-200 p-4 animate-in fade-in-0 slide-in-from-top-2 duration-300">
                <div className="flex items-center space-x-2">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-error-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-sm font-medium text-error-700">{error}</div>
                </div>
              </div>
            )}

            {/* Champ email avec icône */}
            <div className="space-y-1">
              <Input
                label="Adresse email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                placeholder="<EMAIL>"
                startIcon={<EnvelopeIcon className="h-5 w-5" />}
                size="lg"
                className="transition-all duration-200 focus:scale-[1.02]"
              />
            </div>

            {/* Champ mot de passe avec icône et toggle */}
            <div className="space-y-1">
              <Input
                label="Mot de passe"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="••••••••"
                startIcon={<LockClosedIcon className="h-5 w-5" />}
                size="lg"
                className="transition-all duration-200 focus:scale-[1.02]"
                endIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-neutral-400 hover:text-neutral-600 transition-colors duration-200 p-1 rounded-md hover:bg-neutral-100"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5" />
                    ) : (
                      <EyeIcon className="h-5 w-5" />
                    )}
                  </button>
                }
              />
            </div>

            {/* Options de connexion modernisées */}
            <div className="flex items-center justify-between pt-2">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded transition-colors duration-200"
                />
                <label htmlFor="remember-me" className="ml-3 block text-sm font-medium text-neutral-700 cursor-pointer">
                  Se souvenir de moi
                </label>
              </div>

              <div className="text-sm">
                <a
                  href="/auth/forgot-password"
                  className="font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200 hover:underline"
                >
                  Mot de passe oublié ?
                </a>
              </div>
            </div>

            {/* Bouton de connexion avec design amélioré */}
            <div className="pt-4">
              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                loadingText="Connexion en cours..."
                className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transform transition-all duration-200 hover:scale-[1.02] focus:scale-[1.02] shadow-lg hover:shadow-xl"
              >
                Se connecter
              </Button>
            </div>
          </form>
        </Card>

            {/* Informations supplémentaires modernisées */}
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center space-x-2 text-xs text-neutral-200 drop-shadow-md">
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Application sécurisée</span>
              </div>

              <div className="flex items-center justify-center space-x-6 text-xs text-neutral-300 drop-shadow-sm">
                <span>Version 1.0.0</span>
                <span>•</span>
                <span>Sécurisé par Supabase</span>
                <span>•</span>
                <span>© 2024 SAMATRANSPORT</span>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}
