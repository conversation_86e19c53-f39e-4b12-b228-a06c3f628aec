#!/usr/bin/env node

/**
 * Script pour créer un compte administrateur SAMATRANSPORT Control
 * 
 * Usage: node scripts/create-admin.js
 * 
 * Ce script utilise l'API Supabase Management pour créer un utilisateur administrateur
 * avec les bonnes permissions et le profil associé.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'SamaTransport2024!';

// Informations du profil administrateur
const ADMIN_PROFILE = {
  first_name: 'Administrateur',
  last_name: 'SAMATRANSPORT',
  phone: '+225 07 08 09 10 11',
  role: 'SUPER_ADMIN',
  agency_id: '00000000-0000-0000-0000-000000000001',
  is_active: true
};

async function createAdminUser() {
  try {
    console.log('🚀 Création du compte administrateur SAMATRANSPORT Control...\n');

    // Vérifier les variables d'environnement
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('❌ Variables d\'environnement manquantes. Vérifiez NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_ROLE_KEY');
    }

    // Créer le client Supabase avec la clé service (admin)
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('📧 Création de l\'utilisateur auth...');

    // Créer l'utilisateur dans auth.users
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true, // Confirmer l'email automatiquement
      user_metadata: {
        first_name: ADMIN_PROFILE.first_name,
        last_name: ADMIN_PROFILE.last_name,
        role: ADMIN_PROFILE.role
      }
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('⚠️  L\'utilisateur existe déjà. Tentative de récupération...');
        
        // Récupérer l'utilisateur existant
        const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
        
        if (listError) {
          throw new Error(`Erreur lors de la récupération des utilisateurs: ${listError.message}`);
        }

        const existingUser = existingUsers.users.find(user => user.email === ADMIN_EMAIL);
        
        if (!existingUser) {
          throw new Error('Utilisateur introuvable malgré l\'erreur "already registered"');
        }

        console.log(`✅ Utilisateur existant trouvé: ${existingUser.id}`);
        
        // Vérifier si le profil existe
        const { data: existingProfile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', existingUser.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          throw new Error(`Erreur lors de la vérification du profil: ${profileError.message}`);
        }

        if (existingProfile) {
          console.log('✅ Profil administrateur déjà configuré !');
          console.log('\n📋 Informations de connexion:');
          console.log(`   Email: ${ADMIN_EMAIL}`);
          console.log(`   Mot de passe: ${ADMIN_PASSWORD}`);
          console.log(`   Rôle: ${existingProfile.role}`);
          return;
        }

        // Créer le profil pour l'utilisateur existant
        const { error: insertProfileError } = await supabase
          .from('profiles')
          .insert({
            id: existingUser.id,
            ...ADMIN_PROFILE
          });

        if (insertProfileError) {
          throw new Error(`Erreur lors de la création du profil: ${insertProfileError.message}`);
        }

        console.log('✅ Profil administrateur créé pour l\'utilisateur existant !');
        
      } else {
        throw new Error(`Erreur lors de la création de l'utilisateur: ${authError.message}`);
      }
    } else {
      console.log(`✅ Utilisateur auth créé: ${authUser.user.id}`);
      
      // Le profil sera créé automatiquement par le trigger
      // Attendre un peu pour que le trigger s'exécute
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Vérifier que le profil a été créé
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authUser.user.id)
        .single();

      if (profileError) {
        console.log('⚠️  Le trigger n\'a pas créé le profil. Création manuelle...');
        
        // Créer le profil manuellement
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: authUser.user.id,
            ...ADMIN_PROFILE
          });

        if (insertError) {
          throw new Error(`Erreur lors de la création du profil: ${insertError.message}`);
        }
        
        console.log('✅ Profil administrateur créé manuellement !');
      } else {
        console.log('✅ Profil administrateur créé automatiquement par le trigger !');
      }
    }

    console.log('\n🎉 Compte administrateur configuré avec succès !');
    console.log('\n📋 Informations de connexion:');
    console.log(`   Email: ${ADMIN_EMAIL}`);
    console.log(`   Mot de passe: ${ADMIN_PASSWORD}`);
    console.log(`   Rôle: ${ADMIN_PROFILE.role}`);
    console.log('\n🔗 Vous pouvez maintenant vous connecter à l\'application Control !');

  } catch (error) {
    console.error('\n❌ Erreur lors de la création du compte administrateur:');
    console.error(error.message);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  createAdminUser();
}

module.exports = { createAdminUser };
