'use client';

import { useState } from 'react';
import { useAgencies } from '@/hooks/useAgencies';
import { AgencyCard } from './AgencyCard';
import { AgencyFilters } from './AgencyFilters';
import { AgencyModal } from './AgencyModal';
import type { Database } from '@samatransport/lib-core';

type Agency = Database['public']['Tables']['agencies']['Row'];
type AgencyUpdate = Database['public']['Tables']['agencies']['Update'];

/**
 * Composant principal pour la liste des agences
 */
export function AgenciesList() {
  const {
    agencies,
    loading,
    error,
    fetchAgencies,
    createAgency,
    updateAgency,
    deleteAgency,
    toggleAgencyStatus,
  } = useAgencies();

  const [selectedAgency, setSelectedAgency] = useState<Agency | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');

  /**
   * Ouvre le modal pour créer une nouvelle agence
   */
  const handleCreateAgency = () => {
    setSelectedAgency(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  /**
   * Ouvre le modal pour éditer une agence
   */
  const handleEditAgency = (agency: Agency) => {
    setSelectedAgency(agency);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  /**
   * Ouvre le modal pour voir les détails d'une agence
   */
  const handleViewAgency = (agency: Agency) => {
    setSelectedAgency(agency);
    setModalMode('view');
    setIsModalOpen(true);
  };

  /**
   * Ferme le modal
   */
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedAgency(null);
  };

  /**
   * Gère la suppression d'une agence
   */
  const handleDeleteAgency = async (agency: Agency) => {
    if (window.confirm(`Êtes-vous sûr de vouloir désactiver l'agence "${agency.name}" ?`)) {
      try {
        await deleteAgency(agency.id);
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
      }
    }
  };

  /**
   * Gère le changement de statut d'une agence
   */
  const handleToggleStatus = async (agency: Agency) => {
    try {
      await toggleAgencyStatus(agency.id, !agency.is_active);
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
    }
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Erreur de chargement
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtres */}
      <AgencyFilters onFiltersChange={fetchAgencies} />

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <button
            onClick={handleCreateAgency}
            className="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Nouvelle Agence
          </button>
        </div>

        <div className="text-sm text-neutral-500">
          {agencies.length} agence{agencies.length > 1 ? 's' : ''} trouvée{agencies.length > 1 ? 's' : ''}
        </div>
      </div>

      {/* Liste des agences */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow animate-pulse">
              <div className="p-6">
                <div className="h-4 bg-neutral-200 rounded w-3/4 mb-4"></div>
                <div className="h-3 bg-neutral-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-neutral-200 rounded w-2/3 mb-4"></div>
                <div className="flex justify-between items-center">
                  <div className="h-6 bg-neutral-200 rounded w-16"></div>
                  <div className="h-8 bg-neutral-200 rounded w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : agencies.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-neutral-900">Aucune agence</h3>
          <p className="mt-1 text-sm text-neutral-500">
            Commencez par créer votre première agence.
          </p>
          <div className="mt-6">
            <button
              onClick={handleCreateAgency}
              className="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Créer une agence
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agencies.map((agency) => (
            <AgencyCard
              key={agency.id}
              agency={agency}
              onEdit={handleEditAgency}
              onView={handleViewAgency}
              onDelete={handleDeleteAgency}
              onToggleStatus={handleToggleStatus}
            />
          ))}
        </div>
      )}

      {/* Modal */}
      <AgencyModal
        isOpen={isModalOpen}
        mode={modalMode}
        agency={selectedAgency}
        onClose={handleCloseModal}
        onSave={(data, id) => modalMode === 'create' ? createAgency(data as any) : updateAgency(id!, data as AgencyUpdate)}
      />
    </div>
  );
}
