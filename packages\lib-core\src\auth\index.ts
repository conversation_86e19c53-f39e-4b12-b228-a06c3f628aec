/**
 * Utilitaires d'authentification pour l'écosystème SAMATRANSPORT
 */

import type { UserR<PERSON>, AuthUser } from '../types';

export interface AuthSession {
  user: AuthUser;
  access_token: string;
  refresh_token?: string;
  expires_at: number;
}

// Permissions par rôle
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  SUPER_ADMIN: [
    'agencies:read',
    'agencies:write',
    'agencies:delete',
    'users:read',
    'users:write',
    'users:delete',
    'vehicles:read',
    'vehicles:write',
    'vehicles:delete',
    'routes:read',
    'routes:write',
    'routes:delete',
    'bookings:read',
    'bookings:write',
    'bookings:delete',
    'packages:read',
    'packages:write',
    'packages:delete',
    'reports:read',
    'settings:read',
    'settings:write',
  ],
  ADMIN: [
    'users:read',
    'users:write',
    'vehicles:read',
    'vehicles:write',
    'routes:read',
    'routes:write',
    'bookings:read',
    'bookings:write',
    'packages:read',
    'packages:write',
    'reports:read',
    'settings:read',
  ],
  MANAGER: [
    'vehicles:read',
    'routes:read',
    'bookings:read',
    'bookings:write',
    'packages:read',
    'packages:write',
    'reports:read',
  ],
  AGENT: [
    'bookings:read',
    'bookings:write',
    'packages:read',
    'packages:write',
  ],
  DRIVER: [
    'bookings:read',
    'packages:read',
  ],
};

// Utilitaires d'autorisation
export const hasPermission = (user: AuthUser, permission: string): boolean => {
  if (!user.is_active) return false;

  const userPermissions = ROLE_PERMISSIONS[user.role] || [];
  return userPermissions.includes(permission);
};

export const hasRole = (user: AuthUser, role: UserRole): boolean => {
  return user.role === role;
};

export const hasAnyRole = (user: AuthUser, roles: UserRole[]): boolean => {
  return roles.includes(user.role);
};

export const canAccessAgency = (user: AuthUser, agencyId: string): boolean => {
  // Super admin peut accéder à toutes les agences
  if (user.role === 'SUPER_ADMIN') return true;

  // Les autres utilisateurs ne peuvent accéder qu'à leur agence
  return user.agency_id === agencyId;
};

// Utilitaires de validation de session
export const isSessionValid = (session: AuthSession): boolean => {
  return Date.now() < session.expires_at;
};

export const isSessionExpiringSoon = (session: AuthSession, thresholdMinutes: number = 5): boolean => {
  const thresholdMs = thresholdMinutes * 60 * 1000;
  return Date.now() + thresholdMs > session.expires_at;
};

// Constantes
export const AUTH_STORAGE_KEY = 'samatransport_auth_session';
export const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes
