{"name": "@samatransport/eslint-config-custom", "version": "0.1.0", "description": "Configuration ESLint partagée pour l'écosystème SAMATRANSPORT", "main": "index.js", "files": ["index.js", "base.js", "next.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}, "peerDependencies": {"eslint": "^8.0.0", "typescript": "^5.0.0"}}