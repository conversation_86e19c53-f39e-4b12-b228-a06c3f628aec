#!/usr/bin/env node

/**
 * Script pour appliquer les migrations Supabase via l'API
 * 
 * Usage: node scripts/apply-migrations.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function applyMigrations() {
  try {
    console.log('🚀 Application des migrations SAMATRANSPORT...\n');

    // Vérifier les variables d'environnement
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('❌ Variables d\'environnement manquantes. Vérifiez NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_ROLE_KEY');
    }

    // Créer le client Supabase avec la clé service
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('📂 Lecture des fichiers de migration...');

    // Lire le fichier de migration principal
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20241224000001_initial_schema.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`❌ Fichier de migration introuvable: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('🗄️  Application de la migration initiale...');

    // Exécuter la migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      // Si la fonction exec_sql n'existe pas, essayer avec une requête directe
      console.log('⚠️  Fonction exec_sql non disponible, tentative avec requête directe...');
      
      // Diviser le SQL en requêtes individuelles
      const queries = migrationSQL
        .split(';')
        .map(q => q.trim())
        .filter(q => q.length > 0 && !q.startsWith('--'));

      for (let i = 0; i < queries.length; i++) {
        const query = queries[i];
        if (query) {
          console.log(`   Exécution de la requête ${i + 1}/${queries.length}...`);
          
          const { error: queryError } = await supabase
            .from('_temp')
            .select('*')
            .limit(0); // Cette requête ne fait rien mais teste la connexion

          // Pour les vraies requêtes, nous devons utiliser une approche différente
          // car Supabase ne permet pas d'exécuter du SQL arbitraire via le client JS
          console.log('⚠️  Impossible d\'exécuter du SQL arbitraire via le client JS');
          console.log('📋 Veuillez copier le contenu du fichier de migration dans le SQL Editor de Supabase');
          break;
        }
      }
    } else {
      console.log('✅ Migration appliquée avec succès !');
    }

    // Lire et appliquer le fichier de seed
    console.log('\n📂 Lecture du fichier de seed...');
    
    const seedPath = path.join(__dirname, '..', 'supabase', 'seed', '01_admin_user.sql');
    
    if (fs.existsSync(seedPath)) {
      const seedSQL = fs.readFileSync(seedPath, 'utf8');
      
      console.log('🌱 Application du seed...');
      
      // Même limitation pour les seeds
      console.log('📋 Veuillez également copier le contenu du fichier de seed dans le SQL Editor');
    }

    console.log('\n📋 Instructions manuelles:');
    console.log('1. Ouvrez votre Dashboard Supabase: https://supabase.com/dashboard');
    console.log('2. Allez dans Database > SQL Editor');
    console.log('3. Copiez le contenu de: supabase/migrations/20241224000001_initial_schema.sql');
    console.log('4. Collez et exécutez dans l\'éditeur SQL');
    console.log('5. Répétez avec: supabase/seed/01_admin_user.sql');
    console.log('\n🎯 Ensuite, exécutez: pnpm create-admin');

  } catch (error) {
    console.error('\n❌ Erreur lors de l\'application des migrations:');
    console.error(error.message);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  applyMigrations();
}

module.exports = { applyMigrations };
