-- Seed pour créer un compte administrateur SAMATRANSPORT Control
-- Ce fichier crée un utilisateur administrateur pour tester l'application

-- Insérer une agence principale pour SAMATRANSPORT
INSERT INTO agencies (
  id,
  name,
  code,
  address,
  city,
  country,
  phone,
  email,
  currency,
  is_active
) VALUES (
  '00000000-0000-0000-0000-000000000001',
  'SAMATRANSPORT Siège Social',
  'SAMA-HQ',
  'Boulevard de la République, Plateau',
  'Abidjan',
  'Côte d''Ivoire',
  '+225 27 20 30 40 50',
  '<EMAIL>',
  'XOF',
  true
) ON CONFLICT (id) DO NOTHING;

-- Note: L'utilisateur auth doit être créé via l'interface Supabase ou l'API auth
-- Voici les informations pour créer l'utilisateur administrateur :

/*
INFORMATIONS POUR CRÉER LE COMPTE ADMINISTRATEUR :

Email: <EMAIL>
Mot de passe: SamaTransport2024!

Une fois l'utilisateur créé dans auth.users, exécuter cette requête pour créer le profil :
*/

-- <PERSON>tte requête sera exécutée après la création de l'utilisateur auth
-- Remplacer 'USER_UUID_HERE' par l'UUID réel de l'utilisateur créé

-- INSERT INTO profiles (
--   id,
--   first_name,
--   last_name,
--   phone,
--   role,
--   agency_id,
--   is_active
-- ) VALUES (
--   'USER_UUID_HERE', -- UUID de l'utilisateur auth créé
--   'Administrateur',
--   'SAMATRANSPORT',
--   '+225 07 08 09 10 11',
--   'SUPER_ADMIN',
--   '00000000-0000-0000-0000-000000000001',
--   true
-- );

-- Fonction pour créer un profil administrateur automatiquement
-- Cette fonction sera appelée après la création d'un utilisateur avec l'email <EMAIL>
CREATE OR REPLACE FUNCTION create_admin_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Vérifier si c'est l'email administrateur
  IF NEW.email = '<EMAIL>' THEN
    INSERT INTO profiles (
      id,
      first_name,
      last_name,
      phone,
      role,
      agency_id,
      is_active
    ) VALUES (
      NEW.id,
      'Administrateur',
      'SAMATRANSPORT',
      '+225 07 08 09 10 11',
      'SUPER_ADMIN',
      '00000000-0000-0000-0000-000000000001',
      true
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Créer le trigger pour auto-créer le profil admin
DROP TRIGGER IF EXISTS create_admin_profile_trigger ON auth.users;
CREATE TRIGGER create_admin_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_admin_profile();

-- Politique de sécurité RLS pour les profils
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir leur propre profil
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Politique pour permettre aux admins de voir tous les profils
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('SUPER_ADMIN', 'ADMIN')
    )
  );

-- Politique pour permettre aux utilisateurs de mettre à jour leur propre profil
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Politique pour permettre aux admins de mettre à jour tous les profils
CREATE POLICY "Admins can update all profiles" ON profiles
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('SUPER_ADMIN', 'ADMIN')
    )
  );

-- Politique pour les agences
ALTER TABLE agencies ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux admins de voir toutes les agences
CREATE POLICY "Admins can view all agencies" ON agencies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER')
    )
  );

-- Politique pour permettre aux admins de gérer les agences
CREATE POLICY "Admins can manage agencies" ON agencies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('SUPER_ADMIN', 'ADMIN')
    )
  );
