/**
 * Stores Zustand pour l'écosystème SAMATRANSPORT
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AuthUser, Language, Currency } from '../types';

// Store d'authentification
interface AuthStore {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  setUser: (user: AuthUser | null) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      setUser: (user) =>
        set({
          user,
          isAuthenticated: !!user,
        }),
      setLoading: (isLoading) => set({ isLoading }),
      logout: () =>
        set({
          user: null,
          isAuthenticated: false,
        }),
    }),
    {
      name: 'samatransport-auth',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Store de configuration globale
interface ConfigStore {
  language: Language;
  currency: Currency;
  timezone: string;
  theme: 'light' | 'dark';
  setLanguage: (language: Language) => void;
  setCurrency: (currency: Currency) => void;
  setTimezone: (timezone: string) => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useConfigStore = create<ConfigStore>()(
  persist(
    (set) => ({
      language: 'fr',
      currency: 'XOF',
      timezone: 'Africa/Abidjan',
      theme: 'light',
      setLanguage: (language) => set({ language }),
      setCurrency: (currency) => set({ currency }),
      setTimezone: (timezone) => set({ timezone }),
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: 'samatransport-config',
    }
  )
);

// Store de navigation
interface NavigationStore {
  sidebarOpen: boolean;
  currentPage: string;
  breadcrumbs: Array<{ label: string; href?: string }>;
  setSidebarOpen: (open: boolean) => void;
  setCurrentPage: (page: string) => void;
  setBreadcrumbs: (breadcrumbs: Array<{ label: string; href?: string }>) => void;
}

export const useNavigationStore = create<NavigationStore>((set) => ({
  sidebarOpen: false,
  currentPage: '',
  breadcrumbs: [],
  setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
  setCurrentPage: (currentPage) => set({ currentPage }),
  setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),
}));

// Store de notifications
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

interface NotificationStore {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],
  addNotification: (notification) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification = { ...notification, id };
    
    set((state) => ({
      notifications: [...state.notifications, newNotification],
    }));

    // Auto-remove après la durée spécifiée
    if (notification.duration !== 0) {
      setTimeout(() => {
        get().removeNotification(id);
      }, notification.duration || 5000);
    }
  },
  removeNotification: (id) =>
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    })),
  clearNotifications: () => set({ notifications: [] }),
}));

// Store de cache pour les données
interface CacheStore {
  cache: Record<string, { data: any; timestamp: number; ttl: number }>;
  setCache: (key: string, data: any, ttl?: number) => void;
  getCache: (key: string) => any | null;
  clearCache: (key?: string) => void;
}

export const useCacheStore = create<CacheStore>((set, get) => ({
  cache: {},
  setCache: (key, data, ttl = 5 * 60 * 1000) => {
    set((state) => ({
      cache: {
        ...state.cache,
        [key]: {
          data,
          timestamp: Date.now(),
          ttl,
        },
      },
    }));
  },
  getCache: (key) => {
    const cached = get().cache[key];
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > cached.ttl;
    if (isExpired) {
      get().clearCache(key);
      return null;
    }

    return cached.data;
  },
  clearCache: (key) => {
    if (key) {
      set((state) => {
        const newCache = { ...state.cache };
        delete newCache[key];
        return { cache: newCache };
      });
    } else {
      set({ cache: {} });
    }
  },
}));
