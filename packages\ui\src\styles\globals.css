@tailwind base;
@tailwind components;
@tailwind utilities;

/* Polices personnalisées */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Variables CSS personnalisées */
:root {
  --color-primary: theme('colors.primary.500');
  --color-secondary: theme('colors.secondary.500');
  --color-accent: theme('colors.accent.500');
  --color-success: theme('colors.success.500');
  --color-warning: theme('colors.warning.500');
  --color-error: theme('colors.error.500');

  --shadow-soft: theme('boxShadow.sm');
  --shadow-medium: theme('boxShadow.md');
  --shadow-strong: theme('boxShadow.lg');

  --border-radius: theme('borderRadius.lg');
  --border-radius-sm: theme('borderRadius.md');
  --border-radius-lg: theme('borderRadius.xl');
}

/* Styles de base */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-neutral-50 text-neutral-900 antialiased;
  }

  /* Amélioration de la lisibilité */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-neutral-900;
    line-height: 1.2;
  }

  h1 { @apply text-3xl lg:text-4xl; }
  h2 { @apply text-2xl lg:text-3xl; }
  h3 { @apply text-xl lg:text-2xl; }
  h4 { @apply text-lg lg:text-xl; }
  h5 { @apply text-base lg:text-lg; }
  h6 { @apply text-sm lg:text-base; }

  p {
    @apply text-neutral-700 leading-relaxed;
  }

  /* Liens */
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }

  /* Focus states améliorés */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }

  /* Scrollbars personnalisées */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }
}

/* Composants utilitaires */
@layer components {
  /* Conteneurs */
  .container-samatransport {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-neutral-200;
  }

  .card-hover {
    @apply card hover:shadow-md transition-shadow duration-200;
  }

  /* Boutons de base */
  .btn-base {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* États de chargement */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200 border-t-primary-500;
  }

  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }

  /* Formulaires */
  .form-input {
    @apply block w-full px-3 py-2 border border-neutral-300 rounded-lg shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }

  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }

  .form-error {
    @apply mt-1 text-sm text-error-600;
  }

  /* Animations utilitaires */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* Utilitaires personnalisés */
@layer utilities {
  /* Texte */
  .text-balance {
    text-wrap: balance;
  }

  /* Espacement */
  .space-y-section > * + * {
    @apply mt-16;
  }

  .space-y-content > * + * {
    @apply mt-6;
  }

  /* Responsive */
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* Masquage d'éléments */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  /* Truncate amélioré */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Styles pour l'impression */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    @apply text-black bg-white;
  }

  .card {
    @apply shadow-none border border-neutral-300;
  }
}
