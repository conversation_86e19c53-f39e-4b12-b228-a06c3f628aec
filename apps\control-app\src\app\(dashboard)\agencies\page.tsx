'use client';

import { AdminLayout } from '@/components/layout';
import { AgenciesList } from '@/components/agencies/AgenciesList';
import { AgencyStats } from '@/components/agencies/AgencyStats';

/**
 * Page de gestion des agences
 */
export default function AgenciesPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* En-tête de page */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-neutral-900">
              Gestion des Agences
            </h1>
            <p className="mt-2 text-neutral-600">
              G<PERSON>rez les agences de votre réseau de transport
            </p>
          </div>
        </div>

        {/* Statistiques rapides */}
        <AgencyStats />

        {/* Liste des agences */}
        <AgenciesList />
      </div>
    </AdminLayout>
  );
}
