import { useState, useEffect, useCallback } from 'react';
import { agencyService, type AgencyFilters, type AgencyStats } from '@/services/agencyService';
import type { Database } from '@samatransport/lib-core';

type Agency = Database['public']['Tables']['agencies']['Row'];
type AgencyInsert = Database['public']['Tables']['agencies']['Insert'];
type AgencyUpdate = Database['public']['Tables']['agencies']['Update'];

interface UseAgenciesState {
  agencies: Agency[];
  stats: AgencyStats | null;
  countries: string[];
  loading: boolean;
  error: string | null;
}

interface UseAgenciesActions {
  fetchAgencies: (filters?: AgencyFilters) => Promise<void>;
  fetchStats: () => Promise<void>;
  fetchCountries: () => Promise<void>;
  createAgency: (agency: AgencyInsert) => Promise<Agency>;
  updateAgency: (id: string, updates: AgencyUpdate) => Promise<Agency>;
  deleteAgency: (id: string) => Promise<void>;
  toggleAgencyStatus: (id: string, isActive: boolean) => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Hook pour la gestion des agences
 */
export function useAgencies(initialFilters?: AgencyFilters): UseAgenciesState & UseAgenciesActions {
  const [state, setState] = useState<UseAgenciesState>({
    agencies: [],
    stats: null,
    countries: [],
    loading: false,
    error: null,
  });

  const [filters, setFilters] = useState<AgencyFilters | undefined>(initialFilters);

  /**
   * Récupère la liste des agences
   */
  const fetchAgencies = useCallback(async (newFilters?: AgencyFilters) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const currentFilters = newFilters || filters;
      setFilters(currentFilters);
      
      const agencies = await agencyService.getAgencies(currentFilters);
      setState(prev => ({ ...prev, agencies, loading: false }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Erreur inconnue' 
      }));
    }
  }, [filters]);

  /**
   * Récupère les statistiques
   */
  const fetchStats = useCallback(async () => {
    try {
      const stats = await agencyService.getAgencyStats();
      setState(prev => ({ ...prev, stats }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erreur lors du chargement des statistiques' 
      }));
    }
  }, []);

  /**
   * Récupère la liste des pays
   */
  const fetchCountries = useCallback(async () => {
    try {
      const countries = await agencyService.getCountries();
      setState(prev => ({ ...prev, countries }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erreur lors du chargement des pays' 
      }));
    }
  }, []);

  /**
   * Crée une nouvelle agence
   */
  const createAgency = useCallback(async (agency: AgencyInsert): Promise<Agency> => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const newAgency = await agencyService.createAgency(agency);
      setState(prev => ({ 
        ...prev, 
        agencies: [newAgency, ...prev.agencies],
        loading: false 
      }));
      
      // Rafraîchir les statistiques
      await fetchStats();
      
      return newAgency;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Erreur lors de la création' 
      }));
      throw error;
    }
  }, [fetchStats]);

  /**
   * Met à jour une agence
   */
  const updateAgency = useCallback(async (id: string, updates: AgencyUpdate): Promise<Agency> => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const updatedAgency = await agencyService.updateAgency(id, updates);
      setState(prev => ({ 
        ...prev, 
        agencies: prev.agencies.map(agency => 
          agency.id === id ? updatedAgency : agency
        ),
        loading: false 
      }));
      
      return updatedAgency;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Erreur lors de la mise à jour' 
      }));
      throw error;
    }
  }, []);

  /**
   * Supprime une agence
   */
  const deleteAgency = useCallback(async (id: string): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      await agencyService.deleteAgency(id);
      setState(prev => ({ 
        ...prev, 
        agencies: prev.agencies.filter(agency => agency.id !== id),
        loading: false 
      }));
      
      // Rafraîchir les statistiques
      await fetchStats();
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Erreur lors de la suppression' 
      }));
      throw error;
    }
  }, [fetchStats]);

  /**
   * Active/désactive une agence
   */
  const toggleAgencyStatus = useCallback(async (id: string, isActive: boolean): Promise<void> => {
    try {
      await updateAgency(id, { is_active: isActive });
      await fetchStats();
    } catch (error) {
      throw error;
    }
  }, [updateAgency, fetchStats]);

  /**
   * Rafraîchit toutes les données
   */
  const refresh = useCallback(async () => {
    await Promise.all([
      fetchAgencies(),
      fetchStats(),
      fetchCountries()
    ]);
  }, [fetchAgencies, fetchStats, fetchCountries]);

  // Chargement initial
  useEffect(() => {
    refresh();
  }, []);

  return {
    ...state,
    fetchAgencies,
    fetchStats,
    fetchCountries,
    createAgency,
    updateAgency,
    deleteAgency,
    toggleAgencyStatus,
    refresh,
  };
}
