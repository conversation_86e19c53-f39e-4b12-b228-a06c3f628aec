{"name": "@samatransport/lib-core", "version": "0.1.0", "description": "Bibliothèque de logique métier et utilitaires partagés pour l'écosystème SAMATRANSPORT", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./auth": "./src/auth/index.ts", "./api": "./src/api/index.ts", "./i18n": "./src/i18n/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts"}, "scripts": {"build": "tsc --noEmit", "lint": "eslint src/", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "clean": "rm -rf .turbo && rm -rf node_modules"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.77.2", "date-fns": "^4.1.0", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@types/node": "^22.15.24", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "peerDependencies": {"next": "^14.0.0", "react": "^18.0.0"}, "files": ["src"]}