'use client';

import { useState, useEffect } from 'react';
import { agencyService } from '@/services/agencyService';
import type { Database } from '@samatransport/lib-core';

type Agency = Database['public']['Tables']['agencies']['Row'];
type AgencyInsert = Database['public']['Tables']['agencies']['Insert'];
type AgencyUpdate = Database['public']['Tables']['agencies']['Update'];

interface AgencyModalProps {
  isOpen: boolean;
  mode: 'create' | 'edit' | 'view';
  agency?: Agency | null;
  onClose: () => void;
  onSave: (data: AgencyInsert | AgencyUpdate, id?: string) => Promise<Agency>;
}

/**
 * Modal pour créer, éditer ou voir une agence
 */
export function AgencyModal({ isOpen, mode, agency, onClose, onSave }: AgencyModalProps) {
  const [formData, setFormData] = useState<AgencyInsert>({
    name: '',
    code: '',
    address: '',
    city: '',
    country: "Côte d'Ivoire",
    phone: '',
    email: '',
    currency: 'XOF',
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [codeChecking, setCodeChecking] = useState(false);

  // Réinitialiser le formulaire quand le modal s'ouvre
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && agency) {
        setFormData({
          name: agency.name,
          code: agency.code,
          address: agency.address,
          city: agency.city,
          country: agency.country,
          phone: agency.phone,
          email: agency.email || '',
          currency: agency.currency,
          is_active: agency.is_active,
        });
      } else if (mode === 'create') {
        setFormData({
          name: '',
          code: '',
          address: '',
          city: '',
          country: "Côte d'Ivoire",
          phone: '',
          email: '',
          currency: 'XOF',
          is_active: true,
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, agency]);

  /**
   * Vérifie si le code d'agence est disponible
   */
  const checkCodeAvailability = async (code: string) => {
    if (!code || code.length < 2) return;

    setCodeChecking(true);
    try {
      const isAvailable = await agencyService.isCodeAvailable(
        code,
        mode === 'edit' ? agency?.id : undefined
      );

      if (!isAvailable) {
        setErrors(prev => ({ ...prev, code: 'Ce code est déjà utilisé' }));
      } else {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.code;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du code:', error);
    } finally {
      setCodeChecking(false);
    }
  };

  /**
   * Valide le formulaire
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom est requis';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Le code est requis';
    } else if (formData.code.length < 2) {
      newErrors.code = 'Le code doit contenir au moins 2 caractères';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'L\'adresse est requise';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'La ville est requise';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Le téléphone est requis';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'L\'email n\'est pas valide';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Soumet le formulaire
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      if (mode === 'edit' && agency) {
        await onSave(formData as AgencyUpdate, agency.id);
      } else {
        await onSave(formData);
      }
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setErrors({ submit: 'Erreur lors de la sauvegarde' });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Met à jour un champ du formulaire
   */
  const updateField = (field: keyof AgencyInsert, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Effacer l'erreur du champ modifié
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Vérifier le code en temps réel
    if (field === 'code' && typeof value === 'string') {
      checkCodeAvailability(value);
    }
  };

  if (!isOpen) return null;

  const countries = [
    "Côte d'Ivoire",
    'Guinée',
    'Liberia',
    'Sierra Leone',
    'Mali',
    'Burkina Faso',
    'Ghana',
    'Sénégal'
  ];

  const currencies = [
    { value: 'XOF', label: 'CFA (XOF)' },
    { value: 'GNF', label: 'Franc Guinéen (GNF)' },
    { value: 'LRD', label: 'Dollar Libérien (LRD)' },
    { value: 'SLL', label: 'Leone (SLL)' },
    { value: 'EUR', label: 'Euro (EUR)' },
    { value: 'USD', label: 'Dollar US (USD)' }
  ];

  const isReadOnly = mode === 'view';
  const title = mode === 'create' ? 'Nouvelle Agence' :
                mode === 'edit' ? 'Modifier l\'Agence' :
                'Détails de l\'Agence';

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Overlay */}
        <div
          className="fixed inset-0 bg-neutral-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* En-tête */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-neutral-900">
                {title}
              </h3>
              <button
                onClick={onClose}
                className="text-neutral-400 hover:text-neutral-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Contenu */}
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pb-4 sm:p-6 sm:pt-0">
              <div className="space-y-4">
                {/* Nom */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-neutral-700">
                    Nom de l'agence *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => updateField('name', e.target.value)}
                    disabled={isReadOnly}
                    className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                      errors.name
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                    } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                    placeholder="Ex: Agence Abidjan Centre"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                  )}
                </div>

                {/* Code */}
                <div>
                  <label htmlFor="code" className="block text-sm font-medium text-neutral-700">
                    Code agence *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="code"
                      value={formData.code}
                      onChange={(e) => updateField('code', e.target.value.toUpperCase())}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.code
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                      placeholder="Ex: ABJ01"
                    />
                    {codeChecking && (
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg className="animate-spin h-4 w-4 text-neutral-400" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  {errors.code && (
                    <p className="mt-1 text-sm text-red-600">{errors.code}</p>
                  )}
                </div>

                {/* Adresse */}
                <div>
                  <label htmlFor="address" className="block text-sm font-medium text-neutral-700">
                    Adresse *
                  </label>
                  <textarea
                    id="address"
                    rows={2}
                    value={formData.address}
                    onChange={(e) => updateField('address', e.target.value)}
                    disabled={isReadOnly}
                    className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                      errors.address
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                    } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                    placeholder="Adresse complète de l'agence"
                  />
                  {errors.address && (
                    <p className="mt-1 text-sm text-red-600">{errors.address}</p>
                  )}
                </div>

                {/* Ville et Pays */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-neutral-700">
                      Ville *
                    </label>
                    <input
                      type="text"
                      id="city"
                      value={formData.city}
                      onChange={(e) => updateField('city', e.target.value)}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.city
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                      placeholder="Ex: Abidjan"
                    />
                    {errors.city && (
                      <p className="mt-1 text-sm text-red-600">{errors.city}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-neutral-700">
                      Pays
                    </label>
                    <select
                      id="country"
                      value={formData.country}
                      onChange={(e) => updateField('country', e.target.value)}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        isReadOnly
                          ? 'bg-neutral-50 text-neutral-500 border-neutral-300'
                          : 'bg-white border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      }`}
                    >
                      {countries.map((country) => (
                        <option key={country} value={country}>
                          {country}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Téléphone et Email */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-neutral-700">
                      Téléphone *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => updateField('phone', e.target.value)}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.phone
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                      placeholder="+225 XX XX XX XX"
                    />
                    {errors.phone && (
                      <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-neutral-700">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={formData.email || ''}
                      onChange={(e) => updateField('email', e.target.value)}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.email
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      } ${isReadOnly ? 'bg-neutral-50 text-neutral-500' : 'bg-white'}`}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                    )}
                  </div>
                </div>

                {/* Devise et Statut */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-neutral-700">
                      Devise
                    </label>
                    <select
                      id="currency"
                      value={formData.currency}
                      onChange={(e) => updateField('currency', e.target.value)}
                      disabled={isReadOnly}
                      className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        isReadOnly
                          ? 'bg-neutral-50 text-neutral-500 border-neutral-300'
                          : 'bg-white border-neutral-300 focus:ring-primary-500 focus:border-primary-500'
                      }`}
                    >
                      {currencies.map((currency) => (
                        <option key={currency.value} value={currency.value}>
                          {currency.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {!isReadOnly && (
                    <div>
                      <label htmlFor="is_active" className="block text-sm font-medium text-neutral-700">
                        Statut
                      </label>
                      <select
                        id="is_active"
                        value={formData.is_active ? 'true' : 'false'}
                        onChange={(e) => updateField('is_active', e.target.value === 'true')}
                        className="mt-1 block w-full px-3 py-2 border border-neutral-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      >
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                      </select>
                    </div>
                  )}
                </div>

                {errors.submit && (
                  <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
                    {errors.submit}
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            {!isReadOnly && (
              <div className="bg-neutral-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="submit"
                  disabled={loading || codeChecking}
                  className="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sauvegarde...
                    </>
                  ) : (
                    mode === 'create' ? 'Créer' : 'Sauvegarder'
                  )}
                </button>
                <button
                  type="button"
                  onClick={onClose}
                  className="mt-3 w-full inline-flex justify-center rounded-lg border border-neutral-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-neutral-700 hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Annuler
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
