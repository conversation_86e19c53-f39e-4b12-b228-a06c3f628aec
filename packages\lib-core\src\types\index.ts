/**
 * Types et interfaces pour l'écosystème SAMATRANSPORT
 */

// Types de base de données
export * from './database';

// Types métier
export type Currency = 'XOF' | 'EUR' | 'USD';
export type Language = 'fr' | 'en';
export type UserRole = 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'DRIVER';
export type VehicleType = 'STANDARD' | 'VIP';
export type VehicleStatus = 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE';
export type BookingStatus = 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
export type PackageStatus = 'PENDING' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED';
export type PaymentMethod = 'CASH' | 'CARD' | 'MOBILE_MONEY' | 'BANK_TRANSFER';
export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';

// Interfaces communes
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface Agency extends BaseEntity {
  name: string;
  code: string;
  address: string;
  city: string;
  country: string;
  phone?: string;
  email?: string;
  currency: Currency;
  timezone: string;
  is_active: boolean;
}

export interface Vehicle extends BaseEntity {
  agency_id: string;
  registration_number: string;
  brand: string;
  model: string;
  year: number;
  type: VehicleType;
  capacity: number;
  status: VehicleStatus;
  last_maintenance?: string;
  next_maintenance?: string;
}

export interface Route extends BaseEntity {
  agency_id: string;
  name: string;
  origin: string;
  destination: string;
  distance_km: number;
  estimated_duration: number;
  is_active: boolean;
}

export interface User extends BaseEntity {
  email: string;
  full_name: string;
  role: UserRole;
  agency_id?: string;
  is_active: boolean;
  last_login?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  full_name: string;
  role: UserRole;
  agency_id?: string;
  is_active: boolean;
}

// Types pour les formulaires
export interface CreateAgencyData {
  name: string;
  code: string;
  address: string;
  city: string;
  country: string;
  phone?: string;
  email?: string;
  currency: Currency;
  timezone: string;
}

export interface CreateVehicleData {
  agency_id: string;
  registration_number: string;
  brand: string;
  model: string;
  year: number;
  type: VehicleType;
  capacity: number;
}

export interface CreateRouteData {
  agency_id: string;
  name: string;
  origin: string;
  destination: string;
  distance_km: number;
  estimated_duration: number;
}

export interface CreateUserData {
  email: string;
  full_name: string;
  role: UserRole;
  agency_id?: string;
  password: string;
}

// Types pour les réponses API
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Types pour les erreurs
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}
