/**
 * SAMATRANSPORT Design System
 *
 * Bibliothèque de composants UI réutilisables pour l'écosystème SAMATRANSPORT
 * Basée sur React, TypeScript et Tailwind CSS
 */

// Styles globaux
import './styles/globals.css';

// Utilitaires
export * from './utils';

// Composants de base
export { default as Button, type ButtonProps } from './components/Button';
export {
  default as Card,
  CardHeader,
  CardContent,
  CardFooter,
  type CardProps
} from './components/Card';
export {
  default as Input,
  Textarea,
  type InputProps,
  type TextareaProps
} from './components/Input';

// Composants de formulaire (à ajouter)
// export { default as Select, type SelectProps } from './components/Select';
// export { default as Checkbox, type CheckboxProps } from './components/Checkbox';
// export { default as Radio, type RadioProps } from './components/Radio';

// Composants de navigation (à ajouter)
// export { default as Breadcrumb, type BreadcrumbProps } from './components/Breadcrumb';
// export { default as Pagination, type PaginationProps } from './components/Pagination';
// export { default as Tabs, type TabsProps } from './components/Tabs';

// Composants de feedback (à ajouter)
// export { default as Alert, type AlertProps } from './components/Alert';
// export { default as Toast, type ToastProps } from './components/Toast';
// export { default as Modal, type ModalProps } from './components/Modal';
// export { default as Loading, type LoadingProps } from './components/Loading';

// Composants de données (à ajouter)
// export { default as Table, type TableProps } from './components/Table';
// export { default as Badge, type BadgeProps } from './components/Badge';
// export { default as Avatar, type AvatarProps } from './components/Avatar';

// Composants spécifiques SAMATRANSPORT (à ajouter)
// export { default as SeatMap, type SeatMapProps } from './components/SeatMap';
// export { default as RouteCard, type RouteCardProps } from './components/RouteCard';
// export { default as VehicleCard, type VehicleCardProps } from './components/VehicleCard';
// export { default as BookingCard, type BookingCardProps } from './components/BookingCard';
// export { default as PackageTracker, type PackageTrackerProps } from './components/PackageTracker';

// Hooks utilitaires (à ajouter)
// export { useLocalStorage } from './hooks/useLocalStorage';
// export { useDebounce } from './hooks/useDebounce';
// export { useMediaQuery } from './hooks/useMediaQuery';

// Types et interfaces communes
export type {
  // Types de base
  Currency,
  Language,
  UserRole,
  VehicleType,
  BookingStatus,
  PackageStatus,
  PaymentMethod,
  PaymentStatus,
} from './types';

// Configuration du thème
export { theme } from './theme';
