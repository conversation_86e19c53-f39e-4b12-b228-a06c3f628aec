import { createClientSupabase } from '@/lib/supabase';
import type { Database } from '@samatransport/lib-core';

type Agency = Database['public']['Tables']['agencies']['Row'];
type AgencyInsert = Database['public']['Tables']['agencies']['Insert'];
type AgencyUpdate = Database['public']['Tables']['agencies']['Update'];

export interface AgencyFilters {
  search?: string;
  country?: string;
  currency?: 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD';
  isActive?: boolean;
}

export interface AgencyStats {
  total: number;
  active: number;
  inactive: number;
  countries: number;
}

/**
 * Service pour la gestion des agences
 */
export class AgencyService {
  private supabase = createClientSupabase();

  /**
   * Récupère toutes les agences avec filtres optionnels
   */
  async getAgencies(filters?: AgencyFilters): Promise<Agency[]> {
    let query = this.supabase
      .from('agencies')
      .select('*')
      .order('created_at', { ascending: false });

    // Appliquer les filtres
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,code.ilike.%${filters.search}%,city.ilike.%${filters.search}%`);
    }

    if (filters?.country) {
      query = query.eq('country', filters.country);
    }

    if (filters?.currency) {
      query = query.eq('currency', filters.currency);
    }

    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Erreur lors de la récupération des agences: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Récupère une agence par son ID
   */
  async getAgencyById(id: string): Promise<Agency | null> {
    const { data, error } = await this.supabase
      .from('agencies')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Agence non trouvée
      }
      throw new Error(`Erreur lors de la récupération de l'agence: ${error.message}`);
    }

    return data;
  }

  /**
   * Crée une nouvelle agence
   */
  async createAgency(agency: AgencyInsert): Promise<Agency> {
    const { data, error } = await this.supabase
      .from('agencies')
      .insert(agency)
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur lors de la création de l'agence: ${error.message}`);
    }

    return data;
  }

  /**
   * Met à jour une agence
   */
  async updateAgency(id: string, updates: AgencyUpdate): Promise<Agency> {
    const { data, error } = await this.supabase
      .from('agencies')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Erreur lors de la mise à jour de l'agence: ${error.message}`);
    }

    return data;
  }

  /**
   * Supprime une agence (soft delete en désactivant)
   */
  async deleteAgency(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('agencies')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(`Erreur lors de la suppression de l'agence: ${error.message}`);
    }
  }

  /**
   * Active/désactive une agence
   */
  async toggleAgencyStatus(id: string, isActive: boolean): Promise<Agency> {
    return this.updateAgency(id, { is_active: isActive });
  }

  /**
   * Récupère les statistiques des agences
   */
  async getAgencyStats(): Promise<AgencyStats> {
    const { data, error } = await this.supabase
      .from('agencies')
      .select('is_active, country');

    if (error) {
      throw new Error(`Erreur lors de la récupération des statistiques: ${error.message}`);
    }

    const agencies = data || [];
    const active = agencies.filter(a => a.is_active).length;
    const countries = new Set(agencies.map(a => a.country)).size;

    return {
      total: agencies.length,
      active,
      inactive: agencies.length - active,
      countries
    };
  }

  /**
   * Récupère la liste des pays uniques
   */
  async getCountries(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('agencies')
      .select('country')
      .order('country');

    if (error) {
      throw new Error(`Erreur lors de la récupération des pays: ${error.message}`);
    }

    const countries = [...new Set(data?.map(item => item.country) || [])];
    return countries;
  }

  /**
   * Vérifie si un code d'agence est disponible
   */
  async isCodeAvailable(code: string, excludeId?: string): Promise<boolean> {
    let query = this.supabase
      .from('agencies')
      .select('id')
      .eq('code', code);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Erreur lors de la vérification du code: ${error.message}`);
    }

    return !data || data.length === 0;
  }
}

// Instance singleton du service
export const agencyService = new AgencyService();
