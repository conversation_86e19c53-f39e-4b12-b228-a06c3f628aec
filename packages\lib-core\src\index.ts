/**
 * SAMATRANSPORT Core Library
 *
 * Bibliothèque de logique métier et utilitaires partagés
 * pour l'écosystème SAMATRANSPORT
 */

// Types et interfaces
export * from './types';

// Utilitaires
export * from './utils';

// API et clients
export {
  ApiClient,
  initializeApiClient,
  apiClient,
  type ApiRequestConfig,
} from './api';

// Authentification
export * from './auth';

// Internationalisation
export {
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
  translations,
  getTranslation,
  t,
  formatNumber,
  type TranslationKey,
  type Translations,
} from './i18n';

// Configuration
export * from './config';

// Stores Zustand
export * from './stores';
