'use client';

import { Bars3Icon, BellIcon } from '@heroicons/react/24/outline';
import { UserMenu } from './UserMenu';

interface HeaderProps {
  onMenuClick: () => void;
}

/**
 * Header de l'application Control
 * Contient le bouton menu mobile, les notifications et le menu utilisateur
 */
export function Header({ onMenuClick }: HeaderProps) {
  return (
    <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-neutral-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
      {/* Bouton menu mobile */}
      <button
        type="button"
        className="-m-2.5 p-2.5 text-neutral-700 lg:hidden"
        onClick={onMenuClick}
      >
        <span className="sr-only">Ouvrir la sidebar</span>
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Séparateur */}
      <div className="h-6 w-px bg-neutral-200 lg:hidden" aria-hidden="true" />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        {/* Zone de recherche (optionnelle pour plus tard) */}
        <div className="flex flex-1">
          {/* Placeholder pour une barre de recherche globale */}
        </div>

        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Notifications */}
          <button
            type="button"
            className="-m-2.5 p-2.5 text-neutral-400 hover:text-neutral-500"
          >
            <span className="sr-only">Voir les notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Séparateur */}
          <div
            className="hidden lg:block lg:h-6 lg:w-px lg:bg-neutral-200"
            aria-hidden="true"
          />

          {/* Menu utilisateur */}
          <UserMenu />
        </div>
      </div>
    </div>
  );
}
