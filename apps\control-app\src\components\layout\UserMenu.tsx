'use client';

import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import {
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useUser, useSupabaseClient } from '@supabase/auth-helpers-react';
import { useRouter } from 'next/navigation';
import { clsx } from 'clsx';

/**
 * Menu utilisateur avec profil et déconnexion
 */
export function UserMenu() {
  const user = useUser();
  const supabase = useSupabaseClient();
  const router = useRouter();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth/login');
  };

  // Si pas d'utilisateur connecté, afficher un placeholder
  if (!user) {
    return (
      <div className="flex items-center">
        <div className="h-8 w-8 rounded-full bg-neutral-200 animate-pulse" />
      </div>
    );
  }

  const userNavigation = [
    {
      name: 'Votre profil',
      href: '/profile',
      icon: UserCircleIcon,
    },
    {
      name: 'Paramètres',
      href: '/settings',
      icon: Cog6ToothIcon,
    },
  ];

  return (
    <Menu as="div" className="relative">
      <Menu.Button className="-m-1.5 flex items-center p-1.5">
        <span className="sr-only">Ouvrir le menu utilisateur</span>
        <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
          <span className="text-sm font-medium text-primary-600">
            {user.email?.charAt(0).toUpperCase() || 'U'}
          </span>
        </div>
        <span className="hidden lg:flex lg:items-center">
          <span
            className="ml-4 text-sm font-semibold leading-6 text-neutral-900"
            aria-hidden="true"
          >
            {user.user_metadata?.full_name || user.email}
          </span>
          <ChevronDownIcon
            className="ml-2 h-5 w-5 text-neutral-400"
            aria-hidden="true"
          />
        </span>
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-neutral-900/5 focus:outline-none">
          {userNavigation.map((item) => (
            <Menu.Item key={item.name}>
              {({ active }) => (
                <a
                  href={item.href}
                  className={clsx(
                    active ? 'bg-neutral-50' : '',
                    'flex items-center px-3 py-1 text-sm leading-6 text-neutral-900'
                  )}
                >
                  <item.icon className="mr-2 h-4 w-4" aria-hidden="true" />
                  {item.name}
                </a>
              )}
            </Menu.Item>
          ))}
          <div className="border-t border-neutral-100 my-1" />
          <Menu.Item>
            {({ active }) => (
              <button
                onClick={handleSignOut}
                className={clsx(
                  active ? 'bg-neutral-50' : '',
                  'flex w-full items-center px-3 py-1 text-sm leading-6 text-neutral-900'
                )}
              >
                <ArrowRightOnRectangleIcon
                  className="mr-2 h-4 w-4"
                  aria-hidden="true"
                />
                Se déconnecter
              </button>
            )}
          </Menu.Item>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
