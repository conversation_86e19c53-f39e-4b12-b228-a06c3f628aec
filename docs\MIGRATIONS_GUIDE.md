# Guide d'Application des Migrations SAMATRANSPORT

Ce guide vous explique comment appliquer les migrations de base de données pour le projet SAMATRANSPORT.

## 🎯 Méthodes Disponibles

### **Méthode 1 : Supabase CLI (Recommandée)**

#### Installation de Supabase CLI

```bash
# Via npm/pnpm
pnpm add -g supabase

# Via Homebrew (macOS)
brew install supabase/tap/supabase

# Via Chocolatey (Windows)
choco install supabase

# Via Scoop (Windows)
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

#### Configuration

```bash
# 1. Se connecter à Supabase
supabase login

# 2. Lier votre projet (remplacez YOUR_PROJECT_ID)
supabase link --project-ref YOUR_PROJECT_ID

# 3. Vérifier la configuration
supabase status
```

#### Application des migrations

```bash
# Option A: Appliquer seulement les nouvelles migrations
pnpm supabase:push

# Option B: Réinitialiser complètement (migrations + seeds)
pnpm supabase:reset

# Option C: Appliquer seulement les seeds
pnpm supabase:seed
```

### **Méthode 2 : Dashboard Supabase (Manuelle)**

#### Étapes détaillées

1. **Ouvrez votre Dashboard Supabase** :
   - Allez sur https://supabase.com/dashboard
   - Sélectionnez votre projet

2. **Accédez à l'éditeur SQL** :
   - Cliquez sur **Database** dans le menu
   - Sélectionnez **SQL Editor**

3. **Appliquez la migration principale** :
   - Ouvrez le fichier `supabase/migrations/20241224000001_initial_schema.sql`
   - Copiez tout le contenu
   - Collez dans l'éditeur SQL
   - Cliquez sur **Run** (ou Ctrl/Cmd + Enter)

4. **Appliquez le seed administrateur** :
   - Ouvrez le fichier `supabase/seed/01_admin_user.sql`
   - Copiez tout le contenu
   - Collez dans l'éditeur SQL
   - Cliquez sur **Run**

### **Méthode 3 : Script Automatisé**

```bash
# Exécuter le script d'application
pnpm apply-migrations
```

## 🔍 Vérification des Migrations

### Vérifier que les tables sont créées

```sql
-- Dans le SQL Editor de Supabase
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;
```

Vous devriez voir ces tables :
- `agencies`
- `audit_logs`
- `bookings`
- `exchange_rates`
- `maintenance`
- `packages`
- `payments`
- `profiles`
- `routes`
- `schedules`
- `trips`
- `vehicles`

### Vérifier les types ENUM

```sql
-- Vérifier les types personnalisés
SELECT typname 
FROM pg_type 
WHERE typtype = 'e'
ORDER BY typname;
```

### Vérifier l'agence principale

```sql
-- Vérifier que l'agence SAMATRANSPORT est créée
SELECT * FROM agencies WHERE code = 'SAMA-HQ';
```

## 🚨 Résolution des Problèmes

### Erreur : "relation does not exist"

```bash
# La migration n'a pas été appliquée correctement
# Réessayez avec la méthode manuelle (Dashboard)
```

### Erreur : "permission denied"

```bash
# Vérifiez vos clés Supabase dans .env.local
# Assurez-vous d'utiliser la SUPABASE_SERVICE_ROLE_KEY
```

### Erreur : "already exists"

```sql
-- Si certains éléments existent déjà, vous pouvez les supprimer :
DROP TABLE IF EXISTS profiles CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;
-- Puis réappliquer la migration
```

### Erreur Supabase CLI : "Project not linked"

```bash
# Relier votre projet
supabase link --project-ref YOUR_PROJECT_ID

# Trouver votre Project ID dans le Dashboard > Settings > General
```

## 📋 Checklist Post-Migration

- [ ] ✅ Toutes les tables sont créées
- [ ] ✅ Les types ENUM sont définis
- [ ] ✅ L'agence principale existe
- [ ] ✅ Les politiques RLS sont activées
- [ ] ✅ Les triggers sont créés
- [ ] ✅ Les index sont en place

## 🎯 Prochaines Étapes

Une fois les migrations appliquées :

1. **Créer le compte administrateur** :
   ```bash
   pnpm create-admin
   ```

2. **Tester la connexion** :
   - Lancez l'app : `pnpm dev`
   - Allez sur : `http://localhost:3000/auth/login`
   - Connectez-vous avec `<EMAIL>`

3. **Vérifier les permissions** :
   - Testez l'accès aux différentes sections
   - Vérifiez que les données s'affichent correctement

## 🔧 Scripts Utiles

```bash
# Voir le statut de Supabase
supabase status

# Voir les migrations appliquées
supabase migration list

# Créer une nouvelle migration
supabase migration new nom_de_la_migration

# Voir les logs en temps réel
supabase logs
```

## 📞 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** dans le Dashboard Supabase
2. **Consultez la documentation** : https://supabase.com/docs
3. **Vérifiez vos variables d'environnement** dans `.env.local`

---

**Note** : Les migrations modifient la structure de votre base de données. Assurez-vous d'avoir une sauvegarde si vous travaillez avec des données importantes !
