{"name": "@samatransport/ui", "version": "0.1.0", "description": "SAMATRANSPORT Design System - Composants UI réutilisables", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./styles": "./src/styles/globals.css"}, "scripts": {"dev": "storybook dev -p 6006", "build": "tsc --noEmit", "build-storybook": "storybook build", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf storybook-static"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "zod": "^3.25.32"}, "devDependencies": {"@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^9.0.0", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^9.0.0", "@storybook/react": "^9.0.0", "@storybook/testing-library": "^0.2.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "storybook": "^9.0.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "files": ["src"]}