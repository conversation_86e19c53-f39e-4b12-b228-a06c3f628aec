# Configuration du Compte Administrateur SAMATRANSPORT Control

Ce guide explique comment créer et configurer un compte administrateur pour l'application SAMATRANSPORT Control.

## 🚀 Méthode Automatique (Recommandée)

### Prérequis

1. **Projet Supabase configuré** avec les migrations appliquées
2. **Variables d'environnement** configurées dans `.env.local`
3. **Dépendances installées** : `pnpm install`

### Étapes

1. **Copiez le fichier d'environnement** :
   ```bash
   cp .env.example .env.local
   ```

2. **Configurez vos variables Supabase** dans `.env.local` :
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. **Appliquez les migrations et seeds** :
   ```bash
   # Appliquer les migrations
   supabase db reset
   
   # Ou appliquer seulement les seeds
   supabase db seed
   ```

4. **C<PERSON>ez le compte administrateur** :
   ```bash
   pnpm create-admin
   ```

5. **Testez la connexion** :
   - Ouvrez l'application Control : `http://localhost:3000/auth/login`
   - Utilisez les identifiants :
     - **Email** : `<EMAIL>`
     - **Mot de passe** : `SamaTransport2024!`

## 🔧 Méthode Manuelle

Si le script automatique ne fonctionne pas, vous pouvez créer le compte manuellement.

### 1. Créer l'utilisateur dans Supabase Dashboard

1. Allez dans votre **Supabase Dashboard**
2. Naviguez vers **Authentication > Users**
3. Cliquez sur **Add user**
4. Remplissez :
   - **Email** : `<EMAIL>`
   - **Password** : `SamaTransport2024!`
   - **Confirm email** : ✅ (coché)

### 2. Créer le profil administrateur

1. Allez dans **Database > SQL Editor**
2. Exécutez cette requête (remplacez `USER_UUID` par l'UUID de l'utilisateur créé) :

```sql
-- Insérer l'agence principale si elle n'existe pas
INSERT INTO agencies (
  id,
  name,
  code,
  address,
  city,
  country,
  phone,
  email,
  currency,
  is_active
) VALUES (
  '00000000-0000-0000-0000-000000000001',
  'SAMATRANSPORT Siège Social',
  'SAMA-HQ',
  'Boulevard de la République, Plateau',
  'Abidjan',
  'Côte d''Ivoire',
  '+225 27 20 30 40 50',
  '<EMAIL>',
  'XOF',
  true
) ON CONFLICT (id) DO NOTHING;

-- Créer le profil administrateur
INSERT INTO profiles (
  id,
  first_name,
  last_name,
  phone,
  role,
  agency_id,
  is_active
) VALUES (
  'USER_UUID_HERE', -- Remplacer par l'UUID réel
  'Administrateur',
  'SAMATRANSPORT',
  '+225 07 08 09 10 11',
  'SUPER_ADMIN',
  '00000000-0000-0000-0000-000000000001',
  true
);
```

## 📋 Informations de Connexion

Une fois le compte créé, vous pouvez vous connecter avec :

- **URL** : `http://localhost:3000/auth/login`
- **Email** : `<EMAIL>`
- **Mot de passe** : `SamaTransport2024!`
- **Rôle** : `SUPER_ADMIN`

## 🔒 Sécurité

### Changement du Mot de Passe

**Important** : Changez le mot de passe par défaut après la première connexion !

1. Connectez-vous avec les identifiants par défaut
2. Allez dans **Profil > Sécurité**
3. Changez le mot de passe
4. Mettez à jour vos variables d'environnement si nécessaire

### Permissions

Le compte administrateur (`SUPER_ADMIN`) a accès à :

- ✅ Gestion des agences
- ✅ Gestion des utilisateurs
- ✅ Gestion des véhicules
- ✅ Gestion des itinéraires
- ✅ Tableaux de bord complets
- ✅ Configuration système
- ✅ Logs d'audit

## 🐛 Dépannage

### Erreur "User already exists"

Si vous obtenez cette erreur, l'utilisateur existe déjà. Vous pouvez :

1. **Réinitialiser le mot de passe** via Supabase Dashboard
2. **Vérifier le profil** dans la table `profiles`
3. **Supprimer et recréer** l'utilisateur si nécessaire

### Erreur de permissions

Si vous ne pouvez pas vous connecter :

1. Vérifiez que les **politiques RLS** sont correctement configurées
2. Vérifiez que le **profil** existe dans la table `profiles`
3. Vérifiez que le **rôle** est bien `SUPER_ADMIN`

### Variables d'environnement

Assurez-vous que toutes les variables Supabase sont correctement configurées :

```bash
# Vérifier les variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY
echo $SUPABASE_SERVICE_ROLE_KEY
```

## 📞 Support

Si vous rencontrez des problèmes, vérifiez :

1. **Logs Supabase** dans le Dashboard
2. **Console du navigateur** pour les erreurs JavaScript
3. **Logs de l'application** Next.js

---

**Note** : Ce compte administrateur est destiné au développement et aux tests. En production, utilisez des mots de passe forts et uniques !
