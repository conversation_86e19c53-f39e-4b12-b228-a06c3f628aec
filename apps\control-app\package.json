{"name": "@samatransport/control-app", "version": "0.1.0", "private": true, "description": "Application Control - Centre de commande et d'administration SAMATRANSPORT", "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next && rm -rf .turbo"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@samatransport/lib-core": "workspace:*", "@samatransport/ui": "workspace:*", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.24", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}