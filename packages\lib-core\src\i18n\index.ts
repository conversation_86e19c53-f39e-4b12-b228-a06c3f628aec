/**
 * Utilitaires d'internationalisation pour l'écosystème SAMATRANSPORT
 */

import type { Language } from '../types';

// Types pour l'i18n
export interface TranslationKey {
  [key: string]: string | TranslationKey;
}

export interface Translations {
  [locale: string]: TranslationKey;
}

// Langues supportées
export const SUPPORTED_LANGUAGES: Language[] = ['fr', 'en'];
export const DEFAULT_LANGUAGE: Language = 'fr';

// Traductions de base (à étendre)
export const translations: Translations = {
  fr: {
    common: {
      save: 'Enregistrer',
      cancel: 'Annuler',
      delete: 'Supprimer',
      edit: 'Modifier',
      add: 'Ajouter',
      search: 'Rechercher',
      loading: 'Chargement...',
      error: 'Erreur',
      success: 'Succès',
      warning: 'Attention',
      info: 'Information',
      yes: 'Oui',
      no: 'Non',
      confirm: 'Confirmer',
      close: 'Fermer',
    },
    navigation: {
      dashboard: 'Tableau de bord',
      agencies: 'Agences',
      fleet: 'Flotte',
      routes: 'Itinéraires',
      schedules: 'Horaires',
      users: 'Utilisateurs',
      roles: 'Rôles',
      pricing: 'Tarifs',
      maintenance: 'Maintenance',
      reports: 'Rapports',
      audit: 'Journal d\'audit',
    },
  },
  en: {
    common: {
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      search: 'Search',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      warning: 'Warning',
      info: 'Information',
      yes: 'Yes',
      no: 'No',
      confirm: 'Confirm',
      close: 'Close',
    },
    navigation: {
      dashboard: 'Dashboard',
      agencies: 'Agencies',
      fleet: 'Fleet',
      routes: 'Routes',
      schedules: 'Schedules',
      users: 'Users',
      roles: 'Roles',
      pricing: 'Pricing',
      maintenance: 'Maintenance',
      reports: 'Reports',
      audit: 'Audit Log',
    },
  },
};

// Utilitaires de traduction
export const getTranslation = (
  key: string,
  locale: Language = DEFAULT_LANGUAGE,
  fallback?: string
): string => {
  const keys = key.split('.');
  let value: any = translations[locale];

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback vers la langue par défaut
      if (locale !== DEFAULT_LANGUAGE) {
        return getTranslation(key, DEFAULT_LANGUAGE, fallback);
      }
      return fallback || key;
    }
  }

  return typeof value === 'string' ? value : fallback || key;
};

// Alias pour une utilisation plus simple
export const t = getTranslation;

// Utilitaires de formatage localisé
export const formatNumber = (
  value: number,
  locale: Language = DEFAULT_LANGUAGE,
  options?: Intl.NumberFormatOptions
): string => {
  const localeMap: Record<Language, string> = {
    fr: 'fr-FR',
    en: 'en-US',
  };

  return new Intl.NumberFormat(localeMap[locale], options).format(value);
};

export const formatCurrency = (
  amount: number,
  currency: string = 'XOF',
  locale: Language = DEFAULT_LANGUAGE
): string => {
  return formatNumber(amount, locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
  });
};

export const formatDate = (
  date: string | Date,
  locale: Language = DEFAULT_LANGUAGE,
  options?: Intl.DateTimeFormatOptions
): string => {
  const localeMap: Record<Language, string> = {
    fr: 'fr-FR',
    en: 'en-US',
  };

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  return new Intl.DateTimeFormat(localeMap[locale], { ...defaultOptions, ...options }).format(
    new Date(date)
  );
};
