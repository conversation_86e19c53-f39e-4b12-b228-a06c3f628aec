{"name": "samatransport-ecosystem", "version": "1.0.0", "description": "Écosystème intégré d'applications pour le transport routier en Afrique de l'Ouest", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "turbo run clean", "type-check": "turbo run type-check", "prepare": "husky install", "create-admin": "node scripts/create-admin.js", "apply-migrations": "node scripts/apply-migrations.js", "supabase:seed": "supabase db seed", "supabase:reset": "supabase db reset", "supabase:push": "supabase db push"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "dotenv": "^16.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tailwindcss/postcss": "^4.1.8", "eslint": "^9.27.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/digitalbridge/samatransport-ecosystem.git"}, "author": "DigitalBridge", "license": "MIT", "keywords": ["transport", "logistics", "africa", "monorepo", "nextjs", "supabase"]}