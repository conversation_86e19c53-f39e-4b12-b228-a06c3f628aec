'use client';

import { useEffect, useState } from 'react';
import { agencyService, type AgencyStats as IAgencyStats } from '@/services/agencyService';

/**
 * Composant d'affichage des statistiques des agences
 */
export function AgencyStats() {
  const [stats, setStats] = useState<IAgencyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await agencyService.getAgencyStats();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur lors du chargement des statistiques');
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, []);

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Erreur de chargement des statistiques
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {/* Total Agences */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 text-sm font-medium">A</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-neutral-500 truncate">
                  Total Agences
                </dt>
                <dd className="text-lg font-medium text-neutral-900">
                  {loading ? (
                    <div className="h-6 bg-neutral-200 rounded animate-pulse w-8"></div>
                  ) : (
                    stats?.total || 0
                  )}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Agences Actives */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-success-100 rounded-full flex items-center justify-center">
                <span className="text-success-600 text-sm font-medium">✓</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-neutral-500 truncate">
                  Actives
                </dt>
                <dd className="text-lg font-medium text-neutral-900">
                  {loading ? (
                    <div className="h-6 bg-neutral-200 rounded animate-pulse w-8"></div>
                  ) : (
                    stats?.active || 0
                  )}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Agences Inactives */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-warning-100 rounded-full flex items-center justify-center">
                <span className="text-warning-600 text-sm font-medium">⏸</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-neutral-500 truncate">
                  Inactives
                </dt>
                <dd className="text-lg font-medium text-neutral-900">
                  {loading ? (
                    <div className="h-6 bg-neutral-200 rounded animate-pulse w-8"></div>
                  ) : (
                    stats?.inactive || 0
                  )}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Nombre de Pays */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <span className="text-accent-600 text-sm font-medium">🌍</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-neutral-500 truncate">
                  Pays
                </dt>
                <dd className="text-lg font-medium text-neutral-900">
                  {loading ? (
                    <div className="h-6 bg-neutral-200 rounded animate-pulse w-8"></div>
                  ) : (
                    stats?.countries || 0
                  )}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
